/**
 * API Configuration
 *
 * This file contains configuration for API endpoints and related settings.
 * It provides a central place to manage all API URLs and can be easily updated
 * for different environments (development, production, etc.)
 */

// Mock Data Flag - default to true for development
const USE_MOCK_DATA = process.env.NEXT_PUBLIC_USE_MOCK_DATA !== 'false';

// Base URL for the backend API
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000/api';

// Authentication endpoints
const AUTH_ENDPOINTS = {
  LOGIN: `${API_BASE_URL}/auth/login`,
  REGISTER: `${API_BASE_URL}/auth/register`,
  REFRESH_TOKEN: `${API_BASE_URL}/auth/refresh-token`,
  LOGOUT: `${API_BASE_URL}/auth/logout`,
  ADMIN_LOGIN: `${API_BASE_URL}/auth/admin/login`,
};

// User endpoints
const USER_ENDPOINTS = {
  PROFILE: `${API_BASE_URL}/users/profile`,
  // Profile completion endpoints
  BASIC_DETAILS: `${API_BASE_URL}/users/basic-details`,
  EDUCATION_CAREER: `${API_BASE_URL}/users/education-career`,
  LOCATION_DETAILS: `${API_BASE_URL}/users/location-details`,
  FAMILY_DETAILS: `${API_BASE_URL}/users/family-details`,
  PARTNER_PREFERENCES: `${API_BASE_URL}/users/partner-preferences`,
  LIFESTYLE_HABITS: `${API_BASE_URL}/users/lifestyle-habits`,
  ABOUT_ME: `${API_BASE_URL}/users/about-me`,
  // Photo endpoints
  PHOTOS: `${API_BASE_URL}/users/photos`,
  PHOTO_DETAILS: (photoId) => `${API_BASE_URL}/users/photos/${photoId}`,
  SET_PRIMARY_PHOTO: (photoId) => `${API_BASE_URL}/users/photos/${photoId}/set-primary`,
  UPDATE_PHOTO_VISIBILITY: (photoId) => `${API_BASE_URL}/users/photos/${photoId}/visibility`,
  // Other endpoints
  MATCHES: `${API_BASE_URL}/matches`,
  SEARCH: `${API_BASE_URL}/search`,
  NOTIFICATIONS: `${API_BASE_URL}/notifications`,
  MESSAGES: `${API_BASE_URL}/messages`,
  CONVERSATIONS: `${API_BASE_URL}/messages/conversations`,
};

// Admin endpoints
const ADMIN_ENDPOINTS = {
  // Dashboard
  DASHBOARD: `${API_BASE_URL}/admin/dashboard`,
  RECENT_USERS: `${API_BASE_URL}/admin/dashboard/recent-users`,
  RECENT_ACTIVITY: `${API_BASE_URL}/admin/dashboard/recent-activity`,

  // User management
  USERS: `${API_BASE_URL}/admin/users`,
  USER_DETAILS: (userId) => `${API_BASE_URL}/admin/users/${userId}`,

  // Verification
  VERIFICATION_QUEUE: `${API_BASE_URL}/admin/verification-queue`,
  VERIFICATION_DOCUMENT: (docId) => `${API_BASE_URL}/admin/verification-queue/${docId}`,

  // Reports
  REPORTED_PROFILES: `${API_BASE_URL}/admin/reported-profiles`,
  REPORT_DETAILS: (reportId) => `${API_BASE_URL}/admin/reported-profiles/${reportId}`,

  // Premium features
  PREMIUM_PLANS: `${API_BASE_URL}/admin/premium-plans`,
  PREMIUM_PLAN_DETAILS: (planId) => `${API_BASE_URL}/admin/premium-plans/${planId}`,

  // Feature management
  FEATURES: `${API_BASE_URL}/admin/features`,
  FEATURE_DETAILS: (featureId) => `${API_BASE_URL}/admin/features/${featureId}`,

  // Promotions
  PROMOTIONS: `${API_BASE_URL}/admin/promotions`,
  PROMOTION_DETAILS: (promoId) => `${API_BASE_URL}/admin/promotions/${promoId}`,

  // Spotlight
  SPOTLIGHT_FEATURES: `${API_BASE_URL}/admin/spotlight-features`,
  SPOTLIGHT_FEATURE_DETAILS: (spotlightId) => `${API_BASE_URL}/admin/spotlight-features/${spotlightId}`,

  // Algorithm settings
  ALGORITHM: `${API_BASE_URL}/admin/algorithm`,
  ALGORITHM_SETTINGS: `${API_BASE_URL}/admin/algorithm/settings`,
  ALGORITHM_SETTING_DETAILS: (settingId) => `${API_BASE_URL}/admin/algorithm/settings/${settingId}`,

  // Preference configuration
  PREFERENCE_CONFIG: `${API_BASE_URL}/admin/preference-config`,
  PREFERENCE_CATEGORY: (categoryId) => `${API_BASE_URL}/admin/preference-config/categories/${categoryId}`,
  PREFERENCE_FIELD: (fieldId) => `${API_BASE_URL}/admin/preference-config/fields/${fieldId}`,

  // Success analytics
  SUCCESS_ANALYTICS: `${API_BASE_URL}/admin/success-analytics`,

  // Charts and data visualization
  CHARTS: `${API_BASE_URL}/admin/charts`,

  // Content management
  PHOTO_MODERATION: `${API_BASE_URL}/admin/photo-moderation`,
  TEXT_MODERATION: `${API_BASE_URL}/admin/text-moderation`,
  SUCCESS_STORIES: `${API_BASE_URL}/admin/success-stories`,
  SUCCESS_STORY_DETAILS: (storyId) => `${API_BASE_URL}/admin/success-stories/${storyId}`,
  BLOG_POSTS: `${API_BASE_URL}/admin/blog-posts`,
  BLOG_POST_DETAILS: (postId) => `${API_BASE_URL}/admin/blog-posts/${postId}`,

  // Biodata templates
  BIODATA_TEMPLATES: `${API_BASE_URL}/admin/biodata-templates`,
  BIODATA_TEMPLATE_DETAILS: (templateId) => `${API_BASE_URL}/admin/biodata-templates/${templateId}`,

  // Financial management
  FINANCIAL: `${API_BASE_URL}/admin/financial`,
  SUBSCRIPTIONS: `${API_BASE_URL}/admin/subscriptions`,
  SUBSCRIPTION_DETAILS: (subId) => `${API_BASE_URL}/admin/subscriptions/${subId}`,
  TRANSACTIONS: `${API_BASE_URL}/admin/transactions`,
  REVENUE_REPORTS: `${API_BASE_URL}/admin/revenue-reports`,

  // Referral program
  REFERRAL_PROGRAMS: `${API_BASE_URL}/admin/referral-programs`,
  REFERRAL_PROGRAM_DETAILS: (programId) => `${API_BASE_URL}/admin/referral-programs/${programId}`,

  // Communication
  NOTIFICATIONS: `${API_BASE_URL}/admin/notifications`,
  NOTIFICATION_DETAILS: (notificationId) => `${API_BASE_URL}/admin/notifications/${notificationId}`,
  EMAIL_TEMPLATES: `${API_BASE_URL}/admin/email-templates`,
  EMAIL_TEMPLATE_DETAILS: (templateId) => `${API_BASE_URL}/admin/email-templates/${templateId}`,

  // System
  SETTINGS: `${API_BASE_URL}/admin/settings`,
  ADMIN_USERS: `${API_BASE_URL}/admin/admin-users`,
  ADMIN_USER_DETAILS: (adminId) => `${API_BASE_URL}/admin/admin-users/${adminId}`,
};

// API request timeout in milliseconds
const API_TIMEOUT = 30000; // 30 seconds

// Mock data utility functions
const mockDataUtils = {
  // Toggle mock data usage
  toggleMockData: () => {
    if (typeof window !== 'undefined') {
      const currentValue = localStorage.getItem('useMockData') === 'false' ? 'false' : 'true';
      const newValue = currentValue === 'true' ? 'false' : 'true';
      localStorage.setItem('useMockData', newValue);

      // Update environment variable for backend
      if (newValue === 'true') {
        localStorage.setItem('USE_MOCK_DATA', 'true');
      } else {
        localStorage.setItem('USE_MOCK_DATA', 'false');
      }

      window.location.reload();
    }
  },

  // Check if mock data is enabled
  isMockDataEnabled: () => {
    if (typeof window !== 'undefined') {
      const storedValue = localStorage.getItem('useMockData');
      const useMockData = storedValue === null ? USE_MOCK_DATA : storedValue !== 'false';

      // Sync with USE_MOCK_DATA for backend compatibility
      if (useMockData) {
        localStorage.setItem('USE_MOCK_DATA', 'true');
      } else {
        localStorage.setItem('USE_MOCK_DATA', 'false');
      }

      return useMockData;
    }
    return USE_MOCK_DATA;
  },

  // Get current mock data status for display
  getMockDataStatus: () => {
    if (typeof window !== 'undefined') {
      const useMockData = mockDataUtils.isMockDataEnabled();
      return {
        enabled: useMockData,
        label: useMockData ? 'Using Mock Data' : 'Using Real API',
        color: useMockData ? 'warning' : 'success'
      };
    }
    return {
      enabled: USE_MOCK_DATA,
      label: USE_MOCK_DATA ? 'Using Mock Data' : 'Using Real API',
      color: USE_MOCK_DATA ? 'warning' : 'success'
    };
  }
};

// Export the configuration
export {
  API_BASE_URL,
  AUTH_ENDPOINTS,
  USER_ENDPOINTS,
  ADMIN_ENDPOINTS,
  API_TIMEOUT,
  USE_MOCK_DATA,
  mockDataUtils
};
