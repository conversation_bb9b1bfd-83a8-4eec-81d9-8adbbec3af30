// src/controllers/admin/biodata.controller.js

const logger = require('../../utils/logger');

// Mock data for biodata templates
const mockBiodataTemplates = [
    {
        id: 1,
        name: "Traditional Maratha Style",
        description: "Classic Maratha style biodata with traditional elements and design",
        previewImage: "/images/biodata-templates/traditional-maratha.jpg",
        designFile: "/templates/biodata/traditional-maratha.html",
        price: 499,
        discountPercent: 10,
        discountedPrice: 449.1,
        isActive: true,
        createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
        updatedAt: new Date(),
        purchaseCount: 45,
        downloadCount: 38,
        revenue: 20205
    },
    {
        id: 2,
        name: "Modern Elegant",
        description: "Contemporary design with elegant typography and clean layout",
        previewImage: "/images/biodata-templates/modern-elegant.jpg",
        designFile: "/templates/biodata/modern-elegant.html",
        price: 599,
        discountPercent: 15,
        discountedPrice: 509.15,
        isActive: true,
        createdAt: new Date(Date.now() - 25 * 24 * 60 * 60 * 1000),
        updatedAt: new Date(),
        purchaseCount: 32,
        downloadCount: 28,
        revenue: 16293
    },
    {
        id: 3,
        name: "Royal Heritage",
        description: "Premium template with royal Maratha heritage design elements",
        previewImage: "/images/biodata-templates/royal-heritage.jpg",
        designFile: "/templates/biodata/royal-heritage.html",
        price: 799,
        discountPercent: 20,
        discountedPrice: 639.2,
        isActive: true,
        createdAt: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000),
        updatedAt: new Date(),
        purchaseCount: 28,
        downloadCount: 25,
        revenue: 17898
    }
];

// Check if mock data should be used
const shouldUseMockData = () => {
    return process.env.NODE_ENV === 'development' || process.env.USE_MOCK_DATA === 'true';
};

/**
 * @description Get all biodata templates
 * @route GET /api/admin/biodata-templates
 */
exports.getBiodataTemplates = async (req, res, next) => {
    try {
        const {
            page = 1,
            limit = 10,
            search = '',
            sortBy = 'createdAt',
            order = 'desc',
            isActive = ''
        } = req.query;

        const pageNum = parseInt(page, 10);
        const limitNum = parseInt(limit, 10);

        if (isNaN(pageNum) || pageNum < 1 || isNaN(limitNum) || limitNum < 1) {
            const error = new Error("Invalid page or limit parameter.");
            error.status = 400;
            return next(error);
        }

        if (shouldUseMockData()) {
            // Return mock data
            let filteredTemplates = [...mockBiodataTemplates];

            // Apply search filter
            if (search) {
                filteredTemplates = filteredTemplates.filter(template =>
                    template.name.toLowerCase().includes(search.toLowerCase()) ||
                    template.description.toLowerCase().includes(search.toLowerCase())
                );
            }

            // Apply isActive filter
            if (isActive !== '') {
                filteredTemplates = filteredTemplates.filter(template =>
                    template.isActive === (isActive === 'true')
                );
            }

            // Apply sorting
            const sortOrder = order.toLowerCase() === 'asc' ? 1 : -1;
            filteredTemplates.sort((a, b) => {
                const aValue = a[sortBy];
                const bValue = b[sortBy];
                if (aValue < bValue) return -1 * sortOrder;
                if (aValue > bValue) return 1 * sortOrder;
                return 0;
            });

            // Apply pagination
            const skip = (pageNum - 1) * limitNum;
            const paginatedTemplates = filteredTemplates.slice(skip, skip + limitNum);

            return res.status(200).json({
                success: true,
                message: "Biodata templates fetched successfully (Mock Data)",
                templates: paginatedTemplates,
                pagination: {
                    currentPage: pageNum,
                    limit: limitNum,
                    totalPages: Math.ceil(filteredTemplates.length / limitNum),
                    totalTemplates: filteredTemplates.length
                },
                useMockData: true
            });
        }

        // Real database implementation
        const prisma = req.prisma;
        const skip = (pageNum - 1) * limitNum;
        const take = limitNum;
        const sortOrder = order.toLowerCase() === 'asc' ? 'asc' : 'desc';

        // Check if BiodataTemplate model exists
        let templateModelExists = true;
        try {
            await prisma.biodataTemplate.findFirst();
        } catch (e) {
            if (e.message.includes('does not exist in the current database')) {
                templateModelExists = false;
            } else {
                throw e;
            }
        }

        if (!templateModelExists) {
            return res.status(200).json({
                success: true,
                message: "Biodata template data not yet available. Please run database migrations first.",
                templates: [],
                pagination: { currentPage: 1, limit: take, totalPages: 0, totalTemplates: 0 },
                useMockData: false
            });
        }

        // Build where clause based on filters
        let whereClause = {};

        if (isActive !== '') {
            whereClause.isActive = isActive === 'true';
        }
        
        if (search) {
            whereClause.OR = [
                {
                    name: {
                        contains: search,
                        mode: 'insensitive'
                    }
                },
                {
                    description: {
                        contains: search,
                        mode: 'insensitive'
                    }
                }
            ];
        }

        // Get total count for pagination
        const totalTemplates = await prisma.biodataTemplate.count({
            where: whereClause
        });

        // Get templates with pagination, sorting, and filtering
        const templates = await prisma.biodataTemplate.findMany({
            where: whereClause,
            orderBy: {
                [sortBy]: sortOrder
            },
            skip,
            take
        });

        // Get usage statistics for each template
        const enhancedTemplates = await Promise.all(templates.map(async (template) => {
            // Get purchase count
            const purchaseCount = await prisma.userBiodata.count({
                where: {
                    templateId: template.id
                }
            });

            // Get download count
            const downloadStats = await prisma.userBiodata.aggregate({
                where: {
                    templateId: template.id
                },
                _sum: {
                    downloadCount: true
                }
            });

            // Get revenue
            const revenueStats = await prisma.userBiodata.aggregate({
                where: {
                    templateId: template.id
                },
                _sum: {
                    pricePaid: true
                }
            });

            return {
                ...template,
                purchaseCount: purchaseCount || 0,
                downloadCount: downloadStats._sum?.downloadCount || 0,
                revenue: revenueStats._sum?.pricePaid || 0
            };
        }));

        const totalPages = Math.ceil(totalTemplates / take);

        res.status(200).json({
            message: "Biodata templates fetched successfully.",
            templates: enhancedTemplates,
            pagination: {
                currentPage: pageNum,
                limit: take,
                totalPages,
                totalTemplates
            }
        });
    } catch (error) {
        console.error("Error fetching biodata templates:", error);
        next(error);
    }
};

/**
 * @description Get biodata template by ID
 * @route GET /api/admin/biodata/templates/:id
 */
exports.getBiodataTemplateById = async (req, res, next) => {
    const prisma = req.prisma;
    const { id } = req.params;

    try {
        const template = await prisma.biodataTemplate.findUnique({
            where: { id }
        });

        if (!template) {
            return res.status(404).json({
                message: "Biodata template not found."
            });
        }

        // Get usage statistics
        const purchaseCount = await prisma.userBiodata.count({
            where: {
                templateId: template.id
            }
        });

        const downloadStats = await prisma.userBiodata.aggregate({
            where: {
                templateId: template.id
            },
            _sum: {
                downloadCount: true
            }
        });

        const revenueStats = await prisma.userBiodata.aggregate({
            where: {
                templateId: template.id
            },
            _sum: {
                pricePaid: true
            }
        });

        const enhancedTemplate = {
            ...template,
            purchaseCount: purchaseCount || 0,
            downloadCount: downloadStats._sum?.downloadCount || 0,
            revenue: revenueStats._sum?.pricePaid || 0
        };

        res.status(200).json({
            message: "Biodata template fetched successfully.",
            template: enhancedTemplate
        });
    } catch (error) {
        console.error("Error fetching biodata template:", error);
        next(error);
    }
};

/**
 * @description Create a new biodata template
 * @route POST /api/admin/biodata/templates
 */
exports.createBiodataTemplate = async (req, res, next) => {
    const prisma = req.prisma;
    const { 
        name, 
        description, 
        previewImage, 
        designFile, 
        price, 
        discountPercent, 
        isActive 
    } = req.body;

    try {
        // Validate required fields
        if (!name || !previewImage || !designFile || !price) {
            return res.status(400).json({
                message: "Name, preview image, design file, and price are required."
            });
        }

        // Parse numeric values
        const parsedPrice = parseFloat(price);
        const parsedDiscountPercent = discountPercent ? parseInt(discountPercent) : null;

        if (isNaN(parsedPrice) || parsedPrice < 0) {
            return res.status(400).json({
                message: "Price must be a valid number greater than or equal to 0."
            });
        }

        if (parsedDiscountPercent !== null && (isNaN(parsedDiscountPercent) || parsedDiscountPercent < 0 || parsedDiscountPercent > 100)) {
            return res.status(400).json({
                message: "Discount percent must be a valid number between 0 and 100."
            });
        }

        // Calculate discounted price if discount percent is provided
        const discountedPrice = parsedDiscountPercent 
            ? parsedPrice * (1 - parsedDiscountPercent / 100) 
            : null;

        // Create the template
        const template = await prisma.biodataTemplate.create({
            data: {
                name,
                description,
                previewImage,
                designFile,
                price: parsedPrice,
                discountPercent: parsedDiscountPercent,
                discountedPrice,
                isActive: isActive === true || isActive === 'true'
            }
        });

        res.status(201).json({
            message: "Biodata template created successfully.",
            template
        });
    } catch (error) {
        console.error("Error creating biodata template:", error);
        next(error);
    }
};

/**
 * @description Update a biodata template
 * @route PUT /api/admin/biodata/templates/:id
 */
exports.updateBiodataTemplate = async (req, res, next) => {
    const prisma = req.prisma;
    const { id } = req.params;
    const { 
        name, 
        description, 
        previewImage, 
        designFile, 
        price, 
        discountPercent, 
        isActive 
    } = req.body;

    try {
        // Check if template exists
        const existingTemplate = await prisma.biodataTemplate.findUnique({
            where: { id }
        });

        if (!existingTemplate) {
            return res.status(404).json({
                message: "Biodata template not found."
            });
        }

        // Prepare update data
        const updateData = {};

        if (name !== undefined) updateData.name = name;
        if (description !== undefined) updateData.description = description;
        if (previewImage !== undefined) updateData.previewImage = previewImage;
        if (designFile !== undefined) updateData.designFile = designFile;
        if (isActive !== undefined) updateData.isActive = isActive === true || isActive === 'true';

        // Handle price and discount updates
        if (price !== undefined) {
            const parsedPrice = parseFloat(price);
            if (isNaN(parsedPrice) || parsedPrice < 0) {
                return res.status(400).json({
                    message: "Price must be a valid number greater than or equal to 0."
                });
            }
            updateData.price = parsedPrice;
        }

        if (discountPercent !== undefined) {
            const parsedDiscountPercent = discountPercent ? parseInt(discountPercent) : null;
            if (parsedDiscountPercent !== null && (isNaN(parsedDiscountPercent) || parsedDiscountPercent < 0 || parsedDiscountPercent > 100)) {
                return res.status(400).json({
                    message: "Discount percent must be a valid number between 0 and 100."
                });
            }
            updateData.discountPercent = parsedDiscountPercent;

            // Recalculate discounted price
            const priceToUse = updateData.price !== undefined ? updateData.price : existingTemplate.price;
            updateData.discountedPrice = parsedDiscountPercent 
                ? priceToUse * (1 - parsedDiscountPercent / 100) 
                : null;
        } else if (updateData.price !== undefined && existingTemplate.discountPercent) {
            // Recalculate discounted price if price changed but discount percent didn't
            updateData.discountedPrice = updateData.price * (1 - existingTemplate.discountPercent / 100);
        }

        // Update the template
        const updatedTemplate = await prisma.biodataTemplate.update({
            where: { id },
            data: updateData
        });

        res.status(200).json({
            message: "Biodata template updated successfully.",
            template: updatedTemplate
        });
    } catch (error) {
        console.error("Error updating biodata template:", error);
        next(error);
    }
};

/**
 * @description Delete a biodata template
 * @route DELETE /api/admin/biodata/templates/:id
 */
exports.deleteBiodataTemplate = async (req, res, next) => {
    const prisma = req.prisma;
    const { id } = req.params;

    try {
        // Check if template exists
        const existingTemplate = await prisma.biodataTemplate.findUnique({
            where: { id }
        });

        if (!existingTemplate) {
            return res.status(404).json({
                message: "Biodata template not found."
            });
        }

        // Check if template is in use
        const usageCount = await prisma.userBiodata.count({
            where: {
                templateId: id
            }
        });

        if (usageCount > 0) {
            return res.status(400).json({
                message: "Cannot delete template as it is in use by users.",
                usageCount
            });
        }

        // Delete the template
        await prisma.biodataTemplate.delete({
            where: { id }
        });

        res.status(200).json({
            message: "Biodata template deleted successfully."
        });
    } catch (error) {
        console.error("Error deleting biodata template:", error);
        next(error);
    }
};
